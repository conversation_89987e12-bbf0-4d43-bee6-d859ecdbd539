import {
  DiceGroupRollButtonSettings,
  DiceGroupSettings,
  DieSettings,
} from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { BladesDie } from "../playables/dice/die/BladesDie.ts";
import { DiceGroupRollButton } from "./DiceGroupRollButton.ts";
import { DiceSet } from "./DiceSet.ts";

const groupRollId = `${DiceGroupRollButton.tag}-blades`;

export class BladesSet extends DiceSet {
  static override get label(): string {
    return `Blades in the Dark™️`;
  }

  static override get translatable(): boolean {
    return false;
  }

  override get columnNumber(): number {
    return 6;
  }

  override get elements(): (
    | DieSettings
    | DiceGroupSettings
    | DiceGroupRollButtonSettings
    | string
  )[] {
    // Create multiple d6 dice for building dice pools
    const dice: DieSettings[] = Array.from({ length: 6 }, () => ({
      type: "die",
      tag: BladesDie.tag,
      name: "d6",
      size: 6,
      single: true,
      singleThrowDisabled: true,
      groupRoll: {
        id: groupRollId,
        optional: true,
      },
    }));

    const groupRollButton: DiceGroupRollButtonSettings = {
      type: "group-button",
      id: groupRollId,
      buttonText: "roll",
      resultConsolidation: { strategy: "name" },
      resultSort: "descending",
      span: 6,
    };

    return [
      ...dice,
      groupRollButton,
    ];
  }
}

safeCustomDefine(BladesSet);
