import {
  DiceGroupRollButtonSettings,
  DiceGroupSettings,
  DieSettings,
} from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { StarWarsAbilityDie } from "../playables/dice/die/StarWarsAbilityDie.ts";
import { StarWarsBoostDie } from "../playables/dice/die/StarWarsBoostDie.ts";
import { StarWarsChallengeDie } from "../playables/dice/die/StarWarsChallengeDie.ts";
import { StarWarsDifficultyDie } from "../playables/dice/die/StarWarsDifficultyDie.ts";
import { StarWarsForceDie } from "../playables/dice/die/StarWarsForceDie.ts";
import { StarWarsProficiencyDie } from "../playables/dice/die/StarWarsProficiencyDie.ts";
import { StarWarsSetbackDie } from "../playables/dice/die/StarWarsSetbackDie.ts";
import { DiceGroupRollButton } from "./DiceGroupRollButton.ts";
import { DiceSet } from "./DiceSet.ts";

const positiveGroupRollId = `${DiceGroupRollButton.tag}-star-wars-positive`;
const negativeGroupRollId = `${DiceGroupRollButton.tag}-star-wars-negative`;
const forceGroupRollId = `${DiceGroupRollButton.tag}-star-wars-force`;

export class StarWarsSet extends DiceSet {
  static override get label(): string {
    return `Star Wars™️ RPG (FFG)`;
  }
  
  static override get translatable(): boolean {
    return false;
  }

  override get columnNumber(): number {
    return 4;
  }

  override get elements(): (
    | DieSettings
    | DiceGroupSettings
    | DiceGroupRollButtonSettings
    | string
  )[] {
    // Positive dice group (Ability, Proficiency, Boost)
    const positiveDice: DieSettings[] = [
      {
        type: "die",
        tag: StarWarsAbilityDie.tag,
        name: "ability",
        size: 8,
        min: 1,
        max: 10,
        groupRoll: {
          id: positiveGroupRollId,
          optional: true,
        },
        singleThrowDisabled: true,
      },
      {
        type: "die",
        tag: StarWarsProficiencyDie.tag,
        name: "proficiency",
        size: 12,
        min: 1,
        max: 10,
        groupRoll: {
          id: positiveGroupRollId,
          optional: true,
        },
        singleThrowDisabled: true,
      },
      {
        type: "die",
        tag: StarWarsBoostDie.tag,
        name: "boost",
        size: 6,
        min: 1,
        max: 10,
        groupRoll: {
          id: positiveGroupRollId,
          optional: true,
        },
        singleThrowDisabled: true,
      },
    ];

    const positiveGroupRollButton: DiceGroupRollButtonSettings = {
      type: "group-button",
      id: positiveGroupRollId,
      buttonText: "roll positive",
      resultConsolidation: { strategy: "name" },
      span: 1,
    };

    const positiveDiceGroup: DiceGroupSettings = {
      type: "group",
      legend: "positive dice",
      elements: [...positiveDice, positiveGroupRollButton],
      span: 4,
    };

    // Negative dice group (Difficulty, Challenge, Setback)
    const negativeDice: DieSettings[] = [
      {
        type: "die",
        tag: StarWarsDifficultyDie.tag,
        name: "difficulty",
        size: 8,
        min: 1,
        max: 10,
        groupRoll: {
          id: negativeGroupRollId,
          optional: true,
        },
        singleThrowDisabled: true,
      },
      {
        type: "die",
        tag: StarWarsChallengeDie.tag,
        name: "challenge",
        size: 12,
        min: 1,
        max: 10,
        groupRoll: {
          id: negativeGroupRollId,
          optional: true,
        },
        singleThrowDisabled: true,
      },
      {
        type: "die",
        tag: StarWarsSetbackDie.tag,
        name: "setback",
        size: 6,
        min: 1,
        max: 10,
        groupRoll: {
          id: negativeGroupRollId,
          optional: true,
        },
        singleThrowDisabled: true,
      },
    ];

    const negativeGroupRollButton: DiceGroupRollButtonSettings = {
      type: "group-button",
      id: negativeGroupRollId,
      buttonText: "roll negative",
      resultConsolidation: { strategy: "name" },
      span: 1,
    };

    const negativeDiceGroup: DiceGroupSettings = {
      type: "group",
      legend: "negative dice",
      elements: [...negativeDice, negativeGroupRollButton],
      span: 4,
    };

    // Force dice group
    const forceDie: DieSettings = {
      type: "die",
      tag: StarWarsForceDie.tag,
      name: "force",
      size: 12,
      min: 1,
      max: 10,
      groupRoll: {
        id: forceGroupRollId,
        optional: true,
      },
      singleThrowDisabled: true,
    };

    const forceGroupRollButton: DiceGroupRollButtonSettings = {
      type: "group-button",
      id: forceGroupRollId,
      buttonText: "roll force",
      resultConsolidation: { strategy: "name" },
      span: 3,
    };

    const forceDiceGroup: DiceGroupSettings = {
      type: "group",
      legend: "force dice",
      elements: [forceDie, forceGroupRollButton],
      span: 4,
    };

    return [
      positiveDiceGroup,
      negativeDiceGroup,
      forceDiceGroup,
    ];
  }
}

safeCustomDefine(StarWarsSet);
