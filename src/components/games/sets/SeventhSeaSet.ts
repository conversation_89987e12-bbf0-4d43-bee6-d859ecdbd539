import { DiceGroupSettings, DieSettings } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { SeventhSeaDie } from "../playables/dice/die/SeventhSeaDie.ts";
import { DiceSet } from "./DiceSet.ts";

export class SeventhSeaSet extends DiceSet {
  static override get label(): string {
    return `7th Sea™️`;
  }
  static override get translatable(): boolean {
    return false;
  }
  override get columnNumber(): number {
    return 2;
  }
  override get elements(): (DieSettings | DiceGroupSettings | string)[] {
    return [
      {
        type: "die",
        tag: SeventhSeaDie.tag,
        name: "d10",
        size: 10,
        min: 1,
        max: 20,
        hasSum: true,
      },
      {
        type: "die",
        tag: SeventhSeaDie.tag,
        name: "d10",
        size: 10,
        min: 1,
        max: 20,
        mod: "HS",
        hasSum: true,
      },
    ];
  }
}
safeCustomDefine(SeventhSeaSet);
