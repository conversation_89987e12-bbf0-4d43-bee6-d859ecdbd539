import {
  DiceGroupRollButtonSettings,
  DiceGroupSettings,
  DieSettings,
} from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { OrdemParanormalDie } from "../playables/dice/die/OrdemParanormalDie.ts";
import { DiceSet } from "./DiceSet.ts";
import { DiceGroupRollButton } from "sets/DiceGroupRollButton.ts";

export class OrdemParanormalSet extends DiceSet {
  static override get label(): string {
    return `Ordem Paranormal™️`;
  }
  static override get translatable(): boolean {
    return true;
  }
  override get columnNumber(): number {
    return 3;
  }
  override get elements(): (DieSettings | DiceGroupSettings | string)[] {
    const groupRollId = `${DiceGroupRollButton.tag}-ordem-paranormal`;

    const testDice: DieSettings[] = Array.from({ length: 2 }, () => ({
      type: "die",
      tag: OrdemParanormalDie.tag,
      name: "d20",
      size: 6,
      single: true,
      singleThrowDisabled: true,
      groupRoll: {
        id: groupRollId,
        optional: true,
      },
      span: 2,
    }));

    const groupRollButton: DiceGroupRollButtonSettings = {
      type: "group-button",
      id: groupRollId,
      buttonText: "roll",
      resultConsolidation: { strategy: "name" },
      resultSort: "descending",
      span: 3,
    };

    const testDiceGroup: DiceGroupSettings = {
      type: "group",
      legend: `tests`,
      elements: [...testDice, groupRollButton],
      columNumber: 7,
    };

    const damageGroupRollId =
      `${DiceGroupRollButton.tag}-ordem-paranormal-damage`;
    const damageGroupRollButton: DiceGroupRollButtonSettings = {
      type: "group-button",
      id: damageGroupRollId,
      buttonText: "roll",
      resultConsolidation: { strategy: "name", sum: false },
      resultSort: "descending",
      span: 5,
    };

    const damageDice: DieSettings[] = [4, 6, 8, 10, 12].map((size: number) => ({
      type: "die",
      tag: OrdemParanormalDie.tag,
      name: `d${size}`,
      size,
      min: 0,
      hasSum: true,
      groupRoll: {
        id: damageGroupRollId,
      },
    }));

    const damageDiceGroup: DiceGroupSettings = {
      type: "group",
      legend: `damage`,
      elements: [...damageDice, damageGroupRollButton],
      columNumber: 5,
    };

    return [
      testDiceGroup,
      damageDiceGroup,
    ];
  }
}
safeCustomDefine(OrdemParanormalSet);
