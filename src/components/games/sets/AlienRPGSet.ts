import { DiceGroupSettings, DieSettings } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { AlienRPGDie } from "../playables/dice/die/AlienRPGDie.ts";
import { DiceSet } from "./DiceSet.ts";

export class AlienRPGSet extends DiceSet {
  static override get label(): string {
    return `Alien RPG™️`;
  }
  static override get translatable(): boolean {
    return false;
  }
  override get columnNumber(): number {
    return 2;
  }
  override get elements(): (DieSettings | DiceGroupSettings | string)[] {
    // Base dice - regular d6s for skill checks
    const baseDie: DieSettings = {
      type: "die",
      tag: AlienRPGDie.tag,
      name: `base`,
      size: 6,
      min: 1,
      max: 15,
      hasSum: true,
    };

    // Stress dice - d6s that cause panic on 1
    const stressDie: DieSettings = {
      type: "die",
      tag: AlienRPGDie.tag,
      name: `stress`,
      size: 6,
      min: 1,
      max: 15,
      hasSum: true,
    };
    const baseDiceGroup: DiceGroupSettings = {
      type: "group",
      legend: `base`,
      elements: [baseDie],
    };

    const stressDiceGroup: DiceGroupSettings = {
      type: "group",
      legend: `stress`,
      elements: [stressDie],
    };

    return [
      baseDiceGroup,
      stressDiceGroup,
    ];
  }
}
safeCustomDefine(AlienRPGSet);
