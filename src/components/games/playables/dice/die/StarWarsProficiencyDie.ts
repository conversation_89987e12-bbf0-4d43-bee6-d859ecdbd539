import { Tile } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";
import { StarWarsDie } from "./StarWarsDie.ts";

export class StarWarsProficiencyDie extends StarWarsDie {
  override get instanceClassName(): DieClassName {
    return "StarWarsProficiencyDie";
  }

  override get sides(): string[] {
    // Yellow Proficiency Die (d12) faces:
    // 1 face: blank
    // 2 faces: 1 success
    // 2 faces: 2 successes
    // 2 faces: 1 advantage
    // 1 face: 2 advantages
    // 1 face: 1 success + 1 advantage
    // 2 faces: 1 success + 2 advantages
    // 1 face: 1 triumph (critical success)
    return [
      "RPGMeet_StarWars-proficiency-blank.svg",
      "RPGMeet_StarWars-proficiency-success.svg",
      "RPGMeet_StarWars-proficiency-success.svg",
      "RPGMeet_StarWars-proficiency-success-success.svg",
      "RPGMeet_StarWars-proficiency-success-success.svg",
      "RPGMeet_StarWars-proficiency-advantage.svg",
      "RPGMeet_StarWars-proficiency-advantage.svg",
      "RPGMeet_StarWars-proficiency-advantage-advantage.svg",
      "RPGMeet_StarWars-proficiency-success-advantage.svg",
      "RPGMeet_StarWars-proficiency-success-advantage-advantage.svg",
      "RPGMeet_StarWars-proficiency-success-advantage-advantage.svg",
      "RPGMeet_StarWars-proficiency-triumph.svg",
    ];
  }

  override get tile(): Tile {
    return {
      ...super.tile,
      fileName: "RPGMeet_StarWars-proficiency.svg",
    };
  }
}

safeCustomDefine(StarWarsProficiencyDie);
