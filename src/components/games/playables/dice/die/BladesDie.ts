import { GeneratedPicTile } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { GeneratedPicDie } from "dice/die/GeneratedPicDie.ts";
import { DieClassName } from "dice/helpers/diceClassesIndex.ts";

export class BladesDie extends GeneratedPicDie {
  override get instanceClassName(): DieClassName {
    return "BladesDie";
  }
  override get tile(): GeneratedPicTile {
    return {
      ...super.tile,
      imgPath: `public/assets/img/blades`,
      fileName: `RPGMeet_Blades-${this.dieName}.svg`,
      templateName: `RPGMeet_Blades-d${this.dieSize}.svg`,
    };
  }
}

safeCustomDefine(BladesDie);
