import { Tile } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";
import { StarWarsDie } from "./StarWarsDie.ts";

export class StarWarsForceDie extends StarWarsDie {
  override get instanceClassName(): DieClassName {
    return "StarWarsForceDie";
  }

  override get sides(): string[] {
    // White Force Die (d12) faces:
    // 1 face: blank
    // 6 faces: 1 dark side point
    // 1 face: 2 dark side points
    // 2 faces: 1 light side point
    // 2 faces: 2 light side points
    return [
      "RPGMeet_StarWars-force-blank.svg",
      "RPGMeet_StarWars-force-dark.svg",
      "RPGMeet_StarWars-force-dark.svg",
      "RPGMeet_StarWars-force-dark.svg",
      "RPGMeet_StarWars-force-dark.svg",
      "RPGMeet_StarWars-force-dark.svg",
      "RPGMeet_StarWars-force-dark.svg",
      "RPGMeet_StarWars-force-dark-dark.svg",
      "RPGMeet_StarWars-force-light.svg",
      "RPGMeet_StarWars-force-light.svg",
      "RPGMeet_StarWars-force-light-light.svg",
      "RPGMeet_StarWars-force-light-light.svg",
    ];
  }

  override get tile(): Tile {
    return {
      ...super.tile,
      fileName: "RPGMeet_StarWars-force.svg",
    };
  }
}

safeCustomDefine(StarWarsForceDie);
