import { Tile } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";
import { StarWarsDie } from "./StarWarsDie.ts";

export class StarWarsAbilityDie extends StarWarsDie {
  override get instanceClassName(): DieClassName {
    return "StarWarsAbilityDie";
  }

  override get sides(): string[] {
    // Green Ability Die (d8) faces:
    // 1 face: blank
    // 2 faces: 1 success
    // 1 face: 2 successes  
    // 2 faces: 1 advantage
    // 1 face: 1 success + 1 advantage
    // 1 face: 2 advantages
    return [
      "RPGMeet_StarWars-ability-blank.svg",
      "RPGMeet_StarWars-ability-success.svg",
      "RPGMeet_StarWars-ability-success.svg", 
      "RPGMeet_StarWars-ability-success-success.svg",
      "RPGMeet_StarWars-ability-advantage.svg",
      "RPGMeet_StarWars-ability-advantage.svg",
      "RPGMeet_StarWars-ability-success-advantage.svg",
      "RPGMeet_StarWars-ability-advantage-advantage.svg",
    ];
  }

  override get tile(): Tile {
    return {
      ...super.tile,
      fileName: "RPGMeet_StarWars-ability.svg",
    };
  }
}

safeCustomDefine(StarWarsAbilityDie);
