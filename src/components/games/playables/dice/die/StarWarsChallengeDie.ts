import { Tile } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";
import { StarWarsDie } from "./StarWarsDie.ts";

export class StarWarsChallengeDie extends StarWarsDie {
  override get instanceClassName(): DieClassName {
    return "StarWarsChallengeDie";
  }

  override get sides(): string[] {
    // Red Challenge Die (d12) faces:
    // 1 face: blank
    // 2 faces: 1 failure
    // 2 faces: 2 failures
    // 2 faces: 1 threat
    // 1 face: 2 threats
    // 1 face: 1 failure + 1 threat
    // 2 faces: 1 failure + 2 threats
    // 1 face: 1 despair (critical failure)
    return [
      "RPGMeet_StarWars-challenge-blank.svg",
      "RPGMeet_StarWars-challenge-failure.svg",
      "RPGMeet_StarWars-challenge-failure.svg",
      "RPGMeet_StarWars-challenge-failure-failure.svg",
      "RPGMeet_StarWars-challenge-failure-failure.svg",
      "RPGMeet_StarWars-challenge-threat.svg",
      "RPGMeet_StarWars-challenge-threat.svg",
      "RPGMeet_StarWars-challenge-threat-threat.svg",
      "RPGMeet_StarWars-challenge-failure-threat.svg",
      "RPGMeet_StarWars-challenge-failure-threat-threat.svg",
      "RPGMeet_StarWars-challenge-failure-threat-threat.svg",
      "RPGMeet_StarWars-challenge-despair.svg",
    ];
  }

  override get tile(): Tile {
    return {
      ...super.tile,
      fileName: "RPGMeet_StarWars-challenge.svg",
    };
  }
}

safeCustomDefine(StarWarsChallengeDie);
