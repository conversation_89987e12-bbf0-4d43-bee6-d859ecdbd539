import { Tile } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";
import { StarWarsDie } from "./StarWarsDie.ts";

export class StarWarsSetbackDie extends StarWarsDie {
  override get instanceClassName(): DieClassName {
    return "StarWarsSetbackDie";
  }

  override get sides(): string[] {
    // Black Setback Die (d6) faces:
    // 2 faces: blank
    // 1 face: 1 failure
    // 1 face: 1 threat
    // 2 faces: 1 threat
    return [
      "RPGMeet_StarWars-setback-blank.svg",
      "RPGMeet_StarWars-setback-blank.svg",
      "RPGMeet_StarWars-setback-failure.svg",
      "RPGMeet_StarWars-setback-threat.svg",
      "RPGMeet_StarWars-setback-threat.svg",
      "RPGMeet_StarWars-setback-threat.svg",
    ];
  }

  override get tile(): Tile {
    return {
      ...super.tile,
      fileName: "RPGMeet_StarWars-setback.svg",
    };
  }
}

safeCustomDefine(StarWarsSetbackDie);
