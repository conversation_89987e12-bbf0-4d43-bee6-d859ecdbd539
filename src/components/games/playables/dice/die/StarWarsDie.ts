import { FontStyle, Tile } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { Die } from "../Die.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";

export abstract class StarWarsDie extends Die {
  abstract override get instanceClassName(): DieClassName;
  
  public get fontStyle(): FontStyle {
    return {
      "font-family": `'Oswald'`,
      "font-size": `large`,
      "font-weight": `900`,
      fill: `white`,
      stroke: `white`,
      "stroke-width": `1px`,
    };
  }

  override get tile(): Tile {
    return {
      ...super.tile,
      imgPath: `${super.tile.imgPath}/star-wars`,
      fileName: `RPGMeet_StarWars-${this.dieName}.svg`,
    };
  }

  override get sortResults(): "ascending" | "descending" {
    return "descending";
  }
}

safeCustomDefine(StarWarsDie);
