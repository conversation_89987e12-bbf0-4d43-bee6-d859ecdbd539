import { Tile } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";
import { StarWarsDie } from "./StarWarsDie.ts";

export class StarWarsBoostDie extends StarWarsDie {
  override get instanceClassName(): DieClassName {
    return "StarWarsBoostDie";
  }

  override get sides(): string[] {
    // Blue Boost Die (d6) faces:
    // 2 faces: blank
    // 1 face: 1 success
    // 1 face: 1 advantage
    // 2 faces: 1 success + 1 advantage
    return [
      "RPGMeet_StarWars-boost-blank.svg",
      "RPGMeet_StarWars-boost-blank.svg",
      "RPGMeet_StarWars-boost-success.svg",
      "RPGMeet_StarWars-boost-advantage.svg",
      "RPGMeet_StarWars-boost-success-advantage.svg",
      "RPGMeet_StarWars-boost-success-advantage.svg",
    ];
  }

  override get tile(): Tile {
    return {
      ...super.tile,
      fileName: "RPGMeet_StarWars-boost.svg",
    };
  }
}

safeCustomDefine(StarWarsBoostDie);
