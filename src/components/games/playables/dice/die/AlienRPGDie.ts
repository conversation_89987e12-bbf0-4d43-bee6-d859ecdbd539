import { GeneratedPicTile } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";
import { PolyDie } from "dice/die/PolyhedricDie.ts";

export class AlienRPGDie extends PolyDie {
  override get instanceClassName(): DieClassName {
    return "AlienRPGDie";
  }
  override get tile(): GeneratedPicTile {
    const fileName: Record<typeof this.dieName, string> = {
      "base": `RPGMeet_alien-rpg-d${this.dieSize}`,
      "stress": `RPGMeet_alien-rpg-stress-d${this.dieSize}`,
    };
    return {
      ...super.tile,
      imgPath: `public/assets/img/alien-rpg`,
      fileName: `${fileName[this.dieName] || fileName["base"]}.svg`,
      templateName: `${fileName[this.dieName] || fileName["base"]}.svg`,
    };
  }
}
safeCustomDefine(AlienRPGDie);
