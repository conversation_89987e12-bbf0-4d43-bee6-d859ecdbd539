import { Tile } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";
import { StarWarsDie } from "./StarWarsDie.ts";

export class StarWarsDifficultyDie extends StarWarsDie {
  override get instanceClassName(): DieClassName {
    return "StarWarsDifficultyDie";
  }

  override get sides(): string[] {
    // Purple Difficulty Die (d8) faces:
    // 1 face: blank
    // 2 faces: 1 failure
    // 1 face: 2 failures
    // 2 faces: 1 threat
    // 1 face: 1 failure + 1 threat
    // 1 face: 2 threats
    return [
      "RPGMeet_StarWars-difficulty-blank.svg",
      "RPGMeet_StarWars-difficulty-failure.svg",
      "RPGMeet_StarWars-difficulty-failure.svg",
      "RPGMeet_StarWars-difficulty-failure-failure.svg",
      "RPGMeet_StarWars-difficulty-threat.svg",
      "RPGMeet_StarWars-difficulty-threat.svg",
      "RPGMeet_StarWars-difficulty-failure-threat.svg",
      "RPGMeet_StarWars-difficulty-threat-threat.svg",
    ];
  }

  override get tile(): Tile {
    return {
      ...super.tile,
      fileName: "RPGMeet_StarWars-difficulty.svg",
    };
  }
}

safeCustomDefine(StarWarsDifficultyDie);
