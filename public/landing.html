<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#252121">
    <title>RPG Meet - Enhance Your Online RPG Sessions</title>

    <!-- Favicon and app icons -->
    <link
      rel="icon"
      type="image/png"
      href="/public/favicon-96x96.png"
      sizes="96x96"
    >
    <link rel="icon" type="image/svg+xml" href="/public/favicon.svg">
    <link rel="shortcut icon" href="/public/favicon.ico">
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="/public/apple-touch-icon.png"
    >
    <link rel="manifest" href="/public/site.webmanifest">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="/public/tailwind.min.css">
    <link rel="stylesheet" href="/public/animate.css">
    <link rel="stylesheet" href="/public/fonts.css">

    <!-- Icons -->
    <script src="/public/iconify-icon.min.js"></script>

    <style>
      /* Base styles */
      body {
        font-family: "Oswald", sans-serif;
        background: linear-gradient(278deg, #252121, #4c1212, #210101);
        background-size: 180% 180%;
        animation: gradient-animation 24s ease infinite;
        color: white;
        margin: 0;
        padding: 0;
        overflow-x: hidden;
      }

      @keyframes gradient-animation {
        0% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0% 50%;
        }
      }

      /* Header styles */
      header {
        padding: 1.5rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .logo {
        display: flex;
        align-items: center;
        font-size: 2rem;
        font-weight: bold;
      }

      .logo img {
        height: 3rem;
        margin-right: 1rem;
      }

      .header-actions {
        display: flex;
        align-items: center;
        gap: 1rem;
      }

      .translate-button {
        min-width: 120px;
      }

      /* Google Translate customization */
      .goog-te-gadget {
        font-family: "Oswald", sans-serif !important;
        color: white !important;
      }

      .goog-te-gadget-simple {
        background-color: rgba(0, 0, 0, 0.3) !important;
        border: 1px solid rgba(255, 255, 255, 0.3) !important;
        padding: 4px 8px !important;
        border-radius: 4px !important;
        color: white !important;
      }

      .goog-te-menu-value {
        color: white !important;
        text-decoration: none !important;
      }

      .goog-te-menu-value span {
        color: white !important;
      }

      /* Hide Google Translate banner but keep the dropdown */
      .skiptranslate.goog-te-banner-frame {
        display: none !important;
      }

      /* Hide Google automatic translation bar */
      .goog-te-banner-frame.skiptranslate {
        display: none !important;
      }

      /* Hide Google automatic popup/notification */
      .goog-te-balloon-frame {
        display: none !important;
      }

      /* Fix the top padding added by Google Translate */
      body {
        top: 0 !important;
      }

      /* Hero section */
      .hero {
        text-align: center;
        padding: 4rem 2rem;
      }

      .hero h1 {
        font-size: 3.5rem;
        margin-bottom: 1rem;
        line-height: 1.2;
      }

      .hero p {
        font-size: 1.5rem;
        max-width: 800px;
        margin: 0 auto 2rem;
        opacity: 0.9;
      }

      /* Feature section */
      .features {
        padding: 4rem 2rem;
      }

      .features h2 {
        text-align: center;
        font-size: 2.5rem;
        margin-bottom: 3rem;
      }

      .feature-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        max-width: 1200px;
        margin: 0 auto;
      }

      .feature-card {
        background-color: rgba(0, 0, 0, 0.3);
        border-radius: 8px;
        padding: 2rem;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }

      .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
      }

      .feature-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: #e53e3e;
      }

      .feature-card h3 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
      }

      .feature-card p {
        opacity: 0.8;
        line-height: 1.6;
      }

      /* Game sets section */
      .game-sets {
        padding: 4rem 2rem;
        background-color: rgba(0, 0, 0, 0.2);
      }

      .game-sets h2 {
        text-align: center;
        font-size: 2.5rem;
        margin-bottom: 3rem;
      }

      .game-list {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
        max-width: 1200px;
        margin: 0 auto;
      }

      .game-tag {
        background-color: rgba(229, 62, 62, 0.2);
        border: 1px solid rgba(229, 62, 62, 0.5);
        border-radius: 20px;
        padding: 0.5rem 1rem;
        font-size: 1rem;
        transition: background-color 0.3s ease;
      }

      .game-tag:hover {
        background-color: rgba(229, 62, 62, 0.4);
      }

      /* CTA section */
      .cta {
        text-align: center;
        padding: 6rem 2rem;
      }

      .cta h2 {
        font-size: 2.5rem;
        margin-bottom: 1.5rem;
      }

      .cta p {
        font-size: 1.25rem;
        max-width: 600px;
        margin: 0 auto 2rem;
        opacity: 0.9;
      }

      .btn {
        display: inline-block;
        background-color: #e53e3e;
        color: white;
        font-size: 1.25rem;
        font-weight: bold;
        padding: 1rem 2rem;
        border-radius: 8px;
        text-decoration: none;
        transition: transform 0.3s ease, background-color 0.3s ease;
      }

      .btn:hover {
        transform: translateY(-3px);
        background-color: #c53030;
      }

      /* Footer */
      footer {
        background-color: rgba(0, 0, 0, 0.5);
        padding: 2rem;
        text-align: center;
      }

      .footer-links {
        display: flex;
        justify-content: center;
        gap: 2rem;
        margin-bottom: 1.5rem;
      }

      .footer-links a {
        color: white;
        text-decoration: none;
        opacity: 0.8;
        transition: opacity 0.3s ease;
      }

      .footer-links a:hover {
        opacity: 1;
      }

      .copyright {
        opacity: 0.6;
        font-size: 0.9rem;
      }

      /* Responsive adjustments */
      @media (max-width: 768px) {
        .hero h1 {
          font-size: 2.5rem;
        }

        .hero p {
          font-size: 1.25rem;
        }

        .features h2, .game-sets h2, .cta h2 {
          font-size: 2rem;
        }
      }
    </style>
  </head>
  <body>
    <!-- Header -->
    <header>
      <div class="logo">
        <img src="/public/favicon.svg" alt="RPG Meet Logo">
        <span>RPG Meet</span>
      </div>
      <div class="header-actions">
        <div class="translate-button" id="google_translate_element"></div>
        <a href="/" class="btn">Launch App</a>
      </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
      <h1>Enhance Your Online RPG Sessions</h1>
      <p>
        Roll dice, share music, add webcam overlays, and more - all in real-time
        with your gaming group!
      </p>
      <a href="/" class="btn">Get Started for Free</a>
    </section>

    <!-- Features Section -->
    <section class="features">
      <h2>Features</h2>
      <div class="feature-grid">
        <div class="feature-card">
          <div class="feature-icon">
            <iconify-icon icon="mdi:dice-multiple-outline"></iconify-icon>
          </div>
          <h3>Real-Time Dice Rolling</h3>
          <p>
            Roll dice from various game systems and everyone in your session
            will see the results instantly. No more "trust me, I rolled a 20!"
          </p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <iconify-icon icon="mdi:music"></iconify-icon>
          </div>
          <h3>Synchronized Audio</h3>
          <p>
            Share audio from any browser tab or your screen. Everyone hears the
            same music or sound effects simultaneously, creating the perfect
            atmosphere.
          </p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <iconify-icon icon="mdi:camera"></iconify-icon>
          </div>
          <h3>Webcam Overlays</h3>
          <p>
            Add professional-looking banners, tables, and avatars to your webcam
            feed. Apply filters to enhance your stream's appearance.
          </p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <iconify-icon icon="mdi:cards-outline"></iconify-icon>
          </div>
          <h3>Card Decks</h3>
          <p>
            Draw cards from a standard 52-card deck in real-time. Perfect for
            games that use playing cards or for adding random elements to your
            sessions.
          </p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <iconify-icon icon="mdi:account-group"></iconify-icon>
          </div>
          <h3>Easy Sharing</h3>
          <p>
            Invite players to your session with a simple link. No accounts or
            downloads required - just share and play!
          </p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <iconify-icon icon="mdi:translate"></iconify-icon>
          </div>
          <h3>Multilingual Support</h3>
          <p>
            Available in English, French, Italian, Latvian, Spanish, Portuguese,
            and Ukrainian. Play with friends from around the world!
          </p>
        </div>
      </div>
    </section>

    <!-- Game Sets Section -->
    <section class="game-sets">
      <h2>Supported Game Systems</h2>
      <div class="game-list">
        <div class="game-tag">Polyhedric Dice</div>
        <div class="game-tag">Deck of 52 Cards</div>
        <div class="game-tag">7th Sea™</div>
        <div class="game-tag">Achtung! Cthulhu™</div>
        <div class="game-tag">Agon™</div>
        <div class="game-tag">Alien RPG™</div>
        <div class="game-tag">Avatar Legends™</div>
        <div class="game-tag">Blades in the Dark™</div>
        <div class="game-tag">Broken Compass™</div>
        <div class="game-tag">CBR+PNK!™</div>
        <div class="game-tag">City Of Mist™</div>
        <div class="game-tag">Cthulhu Dark™</div>
        <div class="game-tag">Don't Rest Your Head™</div>
        <div class="game-tag">Fantasy World™</div>
        <div class="game-tag">Fate™</div>
        <div class="game-tag">Fiasco™</div>
        <div class="game-tag">GURPS™</div>
        <div class="game-tag">Legend of the Five Rings™</div>
        <div class="game-tag">Not the End™</div>
        <div class="game-tag">Ordem Paranormal™</div>
        <div class="game-tag">PBTA Games</div>
        <div class="game-tag">Six Bullets System™</div>
        <div class="game-tag">Spire™</div>
        <div class="game-tag">Valraven™</div>
        <div class="game-tag">Vampire: the Masquerade™</div>
        <div class="game-tag">Vampire: V20™</div>
        <div class="game-tag">Savage Worlds™</div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="cta">
      <h2>Ready to Elevate Your RPG Experience?</h2>
      <p>
        Join thousands of game masters and players who use RPG Meet to enhance
        their online tabletop sessions.
      </p>
      <a href="/" class="btn">Launch RPG Meet</a>
    </section>

    <!-- Footer -->
    <footer>
      <div class="footer-links">
        <a href="/public/docs/terms-and-conditions.md">Terms & Conditions</a>
        <a href="/public/docs/privacy-policy.md">Privacy Policy</a>
        <a href="/public/docs/cookie-policy.md">Cookie Policy</a>
        <a href="/public/docs/help.md">Help</a>
        <a href="https://github.com/marcomow/rpg-meet" target="_blank"
        >GitHub</a>
      </div>
      <div class="copyright">
        &copy; 2020-2025 RPG Meet. All rights reserved.
      </div>
    </footer>

    <!-- Google Translate Script -->
    <script type="text/javascript">
      function googleTranslateElementInit() {
        const currentURL = new URL(window.location.href);
        const langParam = currentURL.searchParams.get("lang");
        const detectedLanguage = navigator.language.split("-")[0];
        // set the cookie to the detected language to auto-translate
        document.cookie = "googtrans=/en/" +
          (langParam || detectedLanguage);

        new google.translate.TranslateElement(
          {
            pageLanguage: "en",
            layout:
              google.translate.TranslateElement.InlineLayout.SIMPLE,
            autoDisplay: false,
            includedLanguages: "", // all languages
            gaTrack: false,
            gaId: "",
          },
          "google_translate_element",
        );
        // Hide the translation banner but keep the dropdown
        const bannerInterval = setInterval(function () {
          // Hide the banner frame
          const bannerFrame = document.querySelector(
            ".goog-te-banner-frame",
          );
          if (bannerFrame) {
            bannerFrame.style.display = "none";
          }

          // Reset body position
          if (document.body.style.top) {
            document.body.style.top = "0px";
          }

          // Only clear interval when both conditions are met
          if (bannerFrame && document.body.style.top) {
            clearInterval(bannerInterval);
          }
        }, 100);
      }
    </script>
    <script
      type="text/javascript"
      src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"
    ></script>
  </body>
</html>
